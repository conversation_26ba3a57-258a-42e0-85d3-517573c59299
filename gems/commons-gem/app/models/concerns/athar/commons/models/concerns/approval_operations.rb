module Athar
  module Commons
    module Models
      module Concerns
        module ApprovalOperations
          extend ActiveSupport::Concern

          included do
            after_save :notify_approvable, if: -> { saved_change_to_status? }
          end

          # Approve the current step of this request
          # @param user_id [String, Integer] The ID of the user approving the request
          # @param comment [String, nil] Optional comment for the approval
          # @return [Athar::Commons::Services::ApprovalResult] The result of the approval operation
          def approve!(user_id, comment = nil)
            # Check if the request is already approved
            if approved?
              return Athar::Commons::Services::ApprovalResult.failure("This request has already been approved")
            end

            # Check if the request is rejected
            if rejected?
              return Athar::Commons::Services::ApprovalResult.failure("This request has been rejected and cannot be approved")
            end

            # Check if the request is canceled
            if canceled?
              return Athar::Commons::Services::ApprovalResult.failure("This request has been canceled and cannot be approved")
            end

            # Check if the user can approve this request
            unless can_be_approved_by?(user_id)
              return Athar::Commons::Services::ApprovalResult.failure("User is not authorized to approve this request")
            end

            # Get the current step
            step = current_step
            unless step
              return Athar::Commons::Services::ApprovalResult.failure("No active approval step found")
            end

            begin
              # Create the approval action - status update will be handled by the action's callback
              action = approval_actions.create!(
                approval_step: step,
                user_id: user_id,
                action: 'approve',
                comment: comment
              )

              # Return success result with the action
              Athar::Commons::Services::ApprovalResult.success("Request approved successfully", { action: action })
            rescue => e
              # Log the error
              Rails.logger.error "Error approving request: #{e.message}"
              Rails.logger.error e.backtrace.join("\n") if e.backtrace

              # Return failure result
              Athar::Commons::Services::ApprovalResult.failure("Error approving request: #{e.message}",
                                                               { exception: e.class.name })
            end
          end

          # Reject the current step of this request
          # @param user_id [String, Integer] The ID of the user rejecting the request
          # @param comment [String, nil] Optional comment for the rejection
          # @return [Athar::Commons::Services::ApprovalResult] The result of the rejection operation
          def reject!(user_id, comment = nil)
            # Check if the request is already approved
            if approved?
              return Athar::Commons::Services::ApprovalResult.failure("This request has already been approved and cannot be rejected")
            end

            # Check if the request is already rejected
            if rejected?
              return Athar::Commons::Services::ApprovalResult.failure("This request has already been rejected")
            end

            # Check if the request is canceled
            if canceled?
              return Athar::Commons::Services::ApprovalResult.failure("This request has been canceled and cannot be rejected")
            end

            # Check if the user can approve this request
            unless can_be_approved_by?(user_id)
              return Athar::Commons::Services::ApprovalResult.failure("User is not authorized to reject this request")
            end

            # Get the current step
            step = current_step
            unless step
              return Athar::Commons::Services::ApprovalResult.failure("No active approval step found")
            end

            begin
              # Create the rejection action - status update will be handled by the action's callback
              action = approval_actions.create!(
                approval_step: step,
                user_id: user_id,
                action: 'reject',
                comment: comment
              )

              # Return success result with the action
              Athar::Commons::Services::ApprovalResult.success("Request rejected successfully", { action: action })
            rescue => e
              # Log the error
              Rails.logger.error "Error rejecting request: #{e.message}"
              Rails.logger.error e.backtrace.join("\n") if e.backtrace

              # Return failure result
              Athar::Commons::Services::ApprovalResult.failure("Error rejecting request: #{e.message}",
                                                               { exception: e.class.name })
            end
          end

          # Add a comment to the current step of this request
          # @param user_id [String, Integer] The ID of the user adding the comment
          # @param comment_text [String] The comment text
          # @return [Athar::Commons::Services::ApprovalResult] The result of the comment operation
          def comment!(user_id, comment_text)
            # Check if the request is canceled
            if canceled?
              return Athar::Commons::Services::ApprovalResult.failure("This request has been canceled and cannot be commented on")
            end

            # Check if the comment is present
            unless comment_text.present?
              return Athar::Commons::Services::ApprovalResult.failure("Comment text is required")
            end

            # Get the current step
            step = current_step

            # If there's no current step (e.g., request is already approved or rejected),
            # use the last step for the comment
            unless step
              step = approval_steps.order(sequence: :desc).first

              # If there are no steps at all, return an error
              unless step
                return Athar::Commons::Services::ApprovalResult.failure("No approval steps found for this request")
              end
            end

            begin
              # Create the comment action
              action = approval_actions.create!(
                approval_step: step,
                user_id: user_id,
                action: 'comment',
                comment: comment_text
              )

              # Return success result with the action
              Athar::Commons::Services::ApprovalResult.success("Comment added successfully", { action: action })
            rescue => e
              # Log the error
              Rails.logger.error "Error adding comment: #{e.message}"
              Rails.logger.error e.backtrace.join("\n") if e.backtrace

              # Return failure result
              Athar::Commons::Services::ApprovalResult.failure("Error adding comment: #{e.message}",
                                                               { exception: e.class.name })
            end
          end

          # Cancel this request
          # @param user_id [String, Integer] The ID of the user canceling the request
          # @return [Athar::Commons::Services::ApprovalResult] The result of the cancellation operation
          def cancel!(user_id)
            # Check if the request is already approved
            if approved?
              return Athar::Commons::Services::ApprovalResult.failure("This request has already been approved and cannot be canceled")
            end

            # Check if the request is already rejected
            if rejected?
              return Athar::Commons::Services::ApprovalResult.failure("This request has already been rejected and cannot be canceled")
            end

            # Check if the request is already canceled
            if canceled?
              return Athar::Commons::Services::ApprovalResult.failure("This request has already been canceled")
            end

            # Check if the user is the requestor
            unless user_id.to_s == requestor_id.to_s
              return Athar::Commons::Services::ApprovalResult.failure("Only the requestor can cancel this request")
            end

            begin
              # Update the request status
              update!(status: :canceled)

              # Return success result
              Athar::Commons::Services::ApprovalResult.success("Request canceled successfully")
            rescue => e
              # Log the error
              Rails.logger.error "Error canceling request: #{e.message}"
              Rails.logger.error e.backtrace.join("\n") if e.backtrace

              # Return failure result
              Athar::Commons::Services::ApprovalResult.failure("Error canceling request: #{e.message}",
                                                               { exception: e.class.name })
            end
          end

          # Get the current step for this request
          def current_step
            # If the request is already approved or rejected, there's no current step
            return nil if approved? || rejected?

            completed_steps = approval_actions.where(action: %w[approve reject])
                                              .pluck(:approval_step_id).uniq

            # Find the next step that hasn't been completed
            approval_steps
              .where.not(id: completed_steps)
              .order(sequence: :asc)
              .first
          end

          # Get the next step after the given step
          def next_step(step)
            return nil unless step

            completed_steps = approval_actions.where(action: %w[approve reject])
                                              .pluck(:approval_step_id).uniq

            # Find the next step that hasn't been completed
            approval_steps
              .where('sequence > ?', step.sequence)
              .where.not(id: completed_steps)
              .order(sequence: :asc)
              .first
          end

          # Check if a step is complete based on its approval type
          def is_step_complete?(step)
            if step.approval_type == 'any'
              # For 'any' type, one approval/rejection completes the step
              approval_actions.exists?(approval_step: step, action: %w[approve reject])
            else
              # For 'all' type, all potential approvers must have approved
              approver_ids = step.approver_ids
              acted_approvers = approval_actions.where(approval_step: step,
                                                       action: 'approve')
                                                .pluck(:user_id)

              # Check if all approvers have approved
              approver_ids.all? { |id| acted_approvers.include?(id.to_s) }
            end
          end

          # Check if all steps have been completed
          def all_steps_completed?
            completed_steps = approval_actions.where(action: %w[approve reject])
                                              .pluck(:approval_step_id).uniq

            # Check if all steps have been completed
            completed_steps.size == approval_steps.size
          end

          # Check if the request is at its final step
          def final_step?
            step = current_step
            return false unless step

            step.sequence == approval_steps.maximum(:sequence)
          end

          # Check if a user can approve the current step
          def can_be_approved_by?(user_id)
            step = current_step
            return false unless step

            # Check if user is in the approver list
            return false unless step.approver_ids.include?(user_id.to_s)

            # For 'any' type, check if the step has already been approved
            if step.approval_type == 'any'
              !approval_actions.exists?(approval_step: step,
                                        action: %w[approve reject])
            else
              # For 'all' type, check if this specific user has already approved
              !approval_actions.exists?(approval_step: step,
                                        user_id: user_id,
                                        action: %w[approve reject])
            end
          end

          # Check if a step has been rejected
          def step_rejected?(step)
            approval_actions.exists?(approval_step: step, action: 'reject')
          end

          # Finalize the approval request
          def finalize_approval
            previous_status = status
            if update(status: :approved)
              # Force notify approvable if status actually changed
              notify_approvable if previous_status != 'approved'
            end
          end

          # Handle rejection of the request
          def handle_rejection
            update(status: :rejected)
          end

          # Check if any step in the workflow has been rejected
          def any_step_rejected?
            approval_steps.any? { |step| step_rejected?(step) }
          end

          private

          # Notify the approvable instance of status changes
          def notify_approvable
            return unless approvable

            # Get the previous status before the change
            previous_status = status_before_last_save

            # Get the callback from the approvable class
            callback = approvable.class.approval_status_change_callback

            case callback
            when Symbol, String
              # It's a method name - include private methods in respond_to? check
              approvable.send(callback, status, previous_status) if approvable.respond_to?(callback, true)
            when Proc, lambda
              # It's a proc or lambda
              approvable.instance_exec(status, previous_status, &callback)
            end
          end
        end
      end
    end
  end
end
